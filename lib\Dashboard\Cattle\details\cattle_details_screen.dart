import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:provider/provider.dart';
import '../models/cattle_isar.dart';
import '../controllers/cattle_details_controller.dart';
import '../controllers/cattle_controller.dart'; // For ControllerState enum
import '../dialogs/cattle_form_dialog.dart';
import 'cattle_details_overview_tab.dart';
import 'cattle_details_family_tree_tab.dart';
import 'cattle_details_analytics_tab.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_bar.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_tabs.dart';
// Unused import removed
import '../../widgets/state_indicators/universal_state_indicators.dart';
import '../../widgets/mixins/universal_screen_state.dart';
import '../../../core/dependency_injection.dart';

class CattleDetailsScreen extends StatefulWidget {
  final CattleIsar existingCattle;
  final String businessId;
  final Function(CattleIsar)? onCattleUpdated;

  const CattleDetailsScreen({
    Key? key,
    required this.existingCattle,
    required this.businessId,
    this.onCattleUpdated,
  }) : super(key: key);

  @override
  State<CattleDetailsScreen> createState() => _CattleDetailsScreenState();
}

class _CattleDetailsScreenState extends State<CattleDetailsScreen>
    with SingleTickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  UniversalTabManager? _tabManager;

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      debugPrint('DEBUG: CattleDetailsScreen.initState called');
      debugPrint('DEBUG: businessId = ${widget.businessId}');
      debugPrint('DEBUG: existingCattle.name = ${widget.existingCattle.name}');
      debugPrint(
          'DEBUG: existingCattle.businessId = ${widget.existingCattle.businessId}');
    }

    // Overview, Family Tree, and Analytics tabs are available
    _tabController = TabController(
      length: 3, // Overview, Family Tree, and Analytics tabs
      vsync: this,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }





  void _showEditDialog(CattleDetailsController controller) {
    showDialog(
      context: context,
      builder: (dialogContext) => CattleFormDialog(
        cattle: controller.cattle!,
        businessId: widget.businessId,
        animalTypes: controller.animalTypes,
        onSave: (updatedCattle) async {
          try {
            Navigator.of(dialogContext).pop();

            // Show loading indicator
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Row(
                    children: [
                      SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          )),
                      SizedBox(width: 16),
                      Text('Updating cattle...'),
                    ],
                  ),
                  duration: Duration(seconds: 1),
                ),
              );
            }

            await controller.updateCattle(updatedCattle);

            // Notify parent component if callback provided
            if (widget.onCattleUpdated != null && controller.cattle != null) {
              widget.onCattleUpdated!(controller.cattle!);
            }

            if (mounted) {
              CattleMessageUtils.showSuccess(context,
                  CattleMessageUtils.cattleRecordUpdated());
            }

          } catch (e, s) {
            debugPrint('ERROR: Failed to update cattle: $e\n$s');

            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Failed to update cattle: ${e.toString()}'),
                  backgroundColor: Theme.of(context).colorScheme.error,
                ),
              );
            }
          }
        },
      ),
    );
  }

  void _showDeleteDialog(CattleDetailsController controller) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Cattle'),
        content: Text('Are you sure you want to delete ${controller.cattle?.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              // Capture context-dependent objects before async call
              final scaffoldMessenger = ScaffoldMessenger.of(context);
              final theme = Theme.of(context);
              final navigator = Navigator.of(context);
              final cattleName = controller.cattle?.name;

              try {
                Navigator.pop(context); // Close confirmation dialog

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    const SnackBar(
                      content: Row(
                        children: [
                          SizedBox(
                            width: 24,
                            height: 24,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          ),
                          SizedBox(width: 16),
                          Text('Deleting cattle...'),
                        ],
                      ),
                      duration: Duration(seconds: 2),
                    ),
                  );
                }

                await controller.deleteCattle();

                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('$cattleName deleted successfully'),
                      backgroundColor: theme.colorScheme.primary,
                    ),
                  );
                  navigator.pop(); // Return to previous screen
                }
              } catch (e) {
                if (mounted) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete cattle: ${e.toString()}'),
                      backgroundColor: theme.colorScheme.error,
                    ),
                  );
                }
              }
            },
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController(CattleDetailsController controller) {
    switch (controller.state) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if dependencies are initialized before creating controller
    if (!DependencyInjection.isInitialized) {
      return const Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Initializing dependencies...'),
            ],
          ),
        ),
      );
    }

    return ChangeNotifierProvider(
      create: (context) => CattleDetailsController(widget.existingCattle.businessId ?? ''),
      child: Consumer<CattleDetailsController>(
        builder: (context, controller, child) {
          return UniversalStateBuilder(
            state: _getScreenStateFromController(controller),
            errorMessage: controller.errorMessage,
            onRetry: () => executeWithLoading(() => controller.refresh()),
            moduleColor: Colors.blue, // Cattle module color
            loadingWidget: UniversalLoadingIndicator.cattle(),
            errorWidget: UniversalErrorIndicator.cattle(
              message: controller.errorMessage ?? 'Failed to load cattle details',
              onRetry: () => executeWithLoading(() => controller.refresh()),
            ),
            child: Builder(
              builder: (context) {
                // Initialize tab manager here where Provider context is available
                _tabManager ??= UniversalTabManager.threeTabs(
                  controller: _tabController,
                  tabViews: const [
                    CattleDetailsOverviewTab(),
                    CattleDetailsFamilyTreeTab(),
                    CattleDetailsAnalyticsTab(),
                  ],
                  labels: const ['Overview', 'Family Tree', 'Analytics'],
                  icons: const [Icons.info_outline, Icons.account_tree, Icons.analytics],
                  colors: [
                    AppColors.cattleKpiColors[0], // Blue
                    AppColors.cattleKpiColors[1], // Green
                    AppColors.cattleKpiColors[2], // Purple
                  ],
                  showFABs: const [false, false, false], // No FABs on details screen
                  indicatorColor: AppColors.cattleHeader,
                );

                return Scaffold(
                  appBar: AppBarConfig.withBack(
                    context: context,
                    title: '${controller.cattle?.name ?? 'Cattle'} (${controller.cattle?.tagId ?? 'No Tag'})',
                    actions: [
                      IconButton(
                        icon: const Icon(Icons.edit),
                        onPressed: () => _showEditDialog(controller),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _showDeleteDialog(controller),
                      ),
                    ],
                  ),
                  body: _tabManager!,
                );
              },
            ),
          );
        },
      ),
    );
  }
}

