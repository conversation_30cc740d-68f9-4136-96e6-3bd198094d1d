import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import '../models/weight_record_isar.dart';
import '../services/weight_repository.dart';
import '../services/weight_analytics_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_layout.dart';

/// Weight form dialog with reactive updates - no callbacks needed
class WeightFormDialog extends StatefulWidget {
  final List<CattleIsar> cattle;
  final WeightRecordIsar? existingRecord; // For editing
  final CattleIsar? preSelectedCattle; // For pre-selecting specific cattle

  const WeightFormDialog({
    Key? key,
    required this.cattle,
    this.existingRecord,
    this.preSelectedCattle,
  }) : super(key: key);

  @override
  State<WeightFormDialog> createState() => _WeightFormDialogState();
}

class _WeightFormDialogState extends State<WeightFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final WeightRepository _weightRepository = GetIt.I<WeightRepository>();
  final Uuid _uuid = const Uuid();

  // Form controllers
  final _weightController = TextEditingController();
  final _notesController = TextEditingController();
  final _measuredByController = TextEditingController();
  final _bodyConditionNotesController = TextEditingController();

  // Form data with new enum system
  CattleIsar? _selectedCattle;
  DateTime _measurementDate = DateTime.now();
  WeightUnit _weightUnit = WeightUnit.kg;
  MeasurementMethod _measurementMethod = MeasurementMethod.scale;
  String _measurementLocation = 'barn';
  MeasurementQuality _measurementQuality = MeasurementQuality.good;
  HealthStatus _healthStatus = HealthStatus.healthy;
  FeedingStatus _feedingStatus = FeedingStatus.normal;
  double? _bodyConditionScore;
  bool _isPregnant = false;
  int? _pregnancyStage;
  Season _season = Season.spring;
  FeedQuality _feedQuality = FeedQuality.good;
  bool _isEstimate = false;
  double _confidenceLevel = 1.0;

  // Loading state
  bool _isSaving = false;

  // Colors - Green theme for header and save button
  static const _headerColor = Color(0xFF2E7D32);         // Green for header and save button
  static const _measurementColor = Color(0xFF7B1FA2);    // Purple for Measurement Details
  static const _notesColor = Color(0xFF1976D2);          // Blue for Notes

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.existingRecord != null) {
      final record = widget.existingRecord!;
      _weightController.text = record.weight.toString();
      _notesController.text = record.notes ?? '';
      _measuredByController.text = record.measuredBy ?? '';
      _bodyConditionNotesController.text = record.bodyConditionNotes ?? '';
      
      // Get cattle from IsarLink relationship
      _selectedCattle = record.cattle.value ?? widget.cattle.first;

      _measurementDate = record.measurementDate ?? DateTime.now();
      _weightUnit = record.weightUnit;
      _measurementMethod = record.measurementMethod;
      _measurementLocation = record.measurementLocation ?? 'barn';
      _measurementQuality = record.measurementQuality;
      _healthStatus = record.healthStatus;
      _feedingStatus = record.feedingStatus;
      _bodyConditionScore = record.bodyConditionScore;
      _isPregnant = record.isPregnant ?? false;
      _pregnancyStage = record.pregnancyStage;
      _season = record.season;
      _feedQuality = record.feedQuality;
      _isEstimate = record.isEstimate ?? false;
      _confidenceLevel = record.confidenceLevel ?? 1.0;
    } else {
      // Use pre-selected cattle if provided, otherwise use first cattle
      _selectedCattle = widget.preSelectedCattle ??
          (widget.cattle.isNotEmpty ? widget.cattle.first : null);
    }
  }

  @override
  void dispose() {
    _weightController.dispose();
    _notesController.dispose();
    _measuredByController.dispose();
    _bodyConditionNotesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.existingRecord == null
        ? UniversalFormDialog.add(
            title: 'Add Weight Record',
            formContent: _buildFormContent(),
            onCancel: () => Navigator.of(context).pop(),
            onAdd: _saveRecord,
            addText: 'Save',
            isAdding: _isSaving,
          )
        : UniversalFormDialog.edit(
            title: 'Edit Weight Record',
            formContent: _buildFormContent(),
            onCancel: () => Navigator.of(context).pop(),
            onUpdate: _saveRecord,
            updateText: 'Update',
            isUpdating: _isSaving,
          );
  }

  Widget _buildFormContent() {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildCompactForm(),
        ],
      ),
    );
  }

  // Input styling constants like transaction dialog
  static const _inputDecorationConstraints = BoxConstraints(maxHeight: 56);
  static const _inputContentPadding = EdgeInsets.symmetric(horizontal: 12, vertical: 8);

  Widget _buildCompactForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Cattle selection
        DropdownButtonFormField<CattleIsar>(
          value: _selectedCattle,
          decoration: const InputDecoration(
            labelText: 'Select Cattle',
            border: OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
            prefixIcon: Icon(Icons.pets, color: _headerColor),
            contentPadding: _inputContentPadding,
            constraints: _inputDecorationConstraints,
          ),
          isExpanded: true,
          items: widget.cattle.map((cattle) {
            final cattleName = cattle.name ?? 'Unknown';
            final tagId = cattle.tagId ?? '';
            final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;
            return DropdownMenuItem(
              value: cattle,
              child: Text(displayName, overflow: TextOverflow.ellipsis),
            );
          }).toList(),
          onChanged: (value) => setState(() => _selectedCattle = value),
          validator: (value) => value == null ? 'Please select cattle' : null,
        ),
        const SizedBox(height: 16),

        // Weight input
        Row(
          children: [
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _weightController,
                decoration: const InputDecoration(
                  labelText: 'Weight',
                  border: OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
                  prefixIcon: Icon(Icons.scale, color: _measurementColor),
                  contentPadding: _inputContentPadding,
                  constraints: _inputDecorationConstraints,
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
                validator: (value) {
                  if (value == null || value.isEmpty) return 'Please enter weight';
                  final weight = double.tryParse(value);
                  if (weight == null || weight <= 0) return 'Please enter valid weight';
                  return null;
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: DropdownButtonFormField<WeightUnit>(
                value: _weightUnit,
                decoration: InputDecoration(
                  labelText: 'Unit',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                  contentPadding: _inputContentPadding,
                  constraints: _inputDecorationConstraints,
                ),
                items: WeightUnit.values.map((unit) {
                  return DropdownMenuItem(
                    value: unit,
                    child: Text(unit.displayName),
                  );
                }).toList(),
                onChanged: (value) => setState(() => _weightUnit = value!),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Measurement date
        InkWell(
          onTap: _selectDate,
          child: InputDecorator(
            decoration: const InputDecoration(
              labelText: 'Measurement Date',
              border: OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
              prefixIcon: Icon(Icons.calendar_today, color: _headerColor),
              contentPadding: _inputContentPadding,
              constraints: _inputDecorationConstraints,
            ),
            child: Text(
              DateFormat('MMMM dd, yyyy').format(_measurementDate),
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ),
        const SizedBox(height: 16),

        // Measurement method
        DropdownButtonFormField<MeasurementMethod>(
          value: _measurementMethod,
          decoration: const InputDecoration(
            labelText: 'Measurement Method',
            border: OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
            prefixIcon: Icon(Icons.straighten, color: _measurementColor),
            contentPadding: _inputContentPadding,
            constraints: _inputDecorationConstraints,
          ),
          isExpanded: true,
          items: MeasurementMethod.values.map((method) {
            return DropdownMenuItem(
              value: method,
              child: Text(method.displayName),
            );
          }).toList(),
          onChanged: (value) => setState(() => _measurementMethod = value!),
        ),
        const SizedBox(height: 16),

        // Notes
        TextFormField(
          controller: _notesController,
          decoration: const InputDecoration(
            labelText: 'Notes (Optional)',
            border: OutlineInputBorder(borderRadius: BorderRadius.all(Radius.circular(8))),
            prefixIcon: Icon(Icons.note, color: _notesColor),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 12),
          ),
          maxLines: 3,
        ),
      ],
    );
  }





  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _measurementDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    
    if (date != null) {
      setState(() => _measurementDate = date);
    }
  }

  Future<void> _saveRecord() async {
    if (!_formKey.currentState!.validate()) return;
    if (_selectedCattle == null) return;

    // Context is used with mounted checks, which is safe in StatefulWidget

    setState(() => _isSaving = true);

    try {
      final record = WeightRecordIsar()
        ..weight = double.parse(_weightController.text)
        ..weightUnit = _weightUnit
        ..measurementDate = _measurementDate
        ..measurementMethod = _measurementMethod
        ..measurementLocation = _measurementLocation
        ..measuredBy = _measuredByController.text.isNotEmpty ? _measuredByController.text : null
        ..bodyConditionScore = _bodyConditionScore
        ..bodyConditionNotes = _bodyConditionNotesController.text.isNotEmpty ? _bodyConditionNotesController.text : null
        ..healthStatus = _healthStatus
        ..feedingStatus = _feedingStatus
        ..isPregnant = _isPregnant
        ..pregnancyStage = _pregnancyStage
        ..season = _season
        ..feedQuality = _feedQuality
        ..measurementQuality = _measurementQuality
        ..notes = _notesController.text.isNotEmpty ? _notesController.text : null
        ..isEstimate = _isEstimate
        ..confidenceLevel = _confidenceLevel;

      // Set up IsarLink relationship
      record.cattle.value = _selectedCattle;

      if (widget.existingRecord != null) {
        // Editing existing record
        record.id = widget.existingRecord!.id;
        record.businessId = widget.existingRecord!.businessId;
        record.createdAt = widget.existingRecord!.createdAt;
        record.updatedAt = DateTime.now();
        await _weightRepository.updateRecord(record);
      } else {
        // Adding new record - prepare with analytics service
        record.businessId = _uuid.v4();
        record.createdAt = DateTime.now();
        record.updatedAt = DateTime.now();

        // Get previous record for growth calculations
        final allRecords = await _weightRepository.watchAllWeightRecords().first;
        final cattleRecords = allRecords.where((r) => r.cattle.value?.businessId == _selectedCattle!.businessId).toList();
        final previousRecord = cattleRecords.isEmpty ? null : cattleRecords.first;

        // Use analytics service to populate growth data
        final preparedRecord = WeightAnalyticsService.populateGrowthData(record, previousRecord);

        await _weightRepository.addRecord(preparedRecord);
      }

      if (mounted) {
        MessageUtils.showSuccess(
          context,
          widget.existingRecord != null
              ? 'Weight record updated successfully'
              : 'Weight record added successfully',
        );

        // No callback needed - reactive streams handle updates!
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, 'Error saving weight record: $e');
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }
}
