import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../models/health_record_isar.dart';
import '../controllers/health_controller.dart';
import '../services/health_repository.dart';
// Unused import removed


import 'package:intl/intl.dart';
import '../../../constants/app_tabs.dart';

class HealthRecordsTab extends StatelessWidget {
  final HealthController controller;

  const HealthRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        final records = controller.healthRecords;

        if (records.isEmpty) {
          return UniversalEmptyState.health(
            title: 'No Health Records',
            message: 'Add your first health record using the + button',
          );
        }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: records.length,
      itemBuilder: (context, index) {
        final record = records[index];
        final cattle = controller.getCattle(record.cattleBusinessId);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: UniversalEmptyStateTheme.health,
              child: Icon(
                _getRecordIcon(record.recordType),
                color: Colors.white,
              ),
            ),
            title: Text(cattle?.name ?? 'Unknown Cattle'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Type: ${record.recordType ?? 'Unknown'}'),
                Text('Date: ${_formatDate(record.date)}'),
                if (record.status != null)
                  Text('Status: ${record.status}'),
              ],
            ),
            trailing: Icon(
              _getStatusIcon(record.status),
              color: _getStatusColor(record.status),
            ),
          ),
        );
      },
    );
      },
    );
  }

  IconData _getRecordIcon(String? recordType) {
    switch (recordType?.toLowerCase()) {
      case 'vaccination':
        return Icons.vaccines;
      case 'treatment':
        return Icons.healing;
      case 'checkup':
        return Icons.medical_services;
      default:
        return Icons.health_and_safety;
    }
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Icons.pending;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}

class HealthRecordsList extends StatefulWidget {
  const HealthRecordsList({Key? key}) : super(key: key);

  @override
  State<HealthRecordsList> createState() => _HealthRecordsListState();
}

class _HealthRecordsListState extends State<HealthRecordsList> {
  // Repository dependencies from GetIt
  late final HealthRepository _healthRepository;
  // Unused repository removed


  List<HealthRecordIsar> _healthRecords = [];
  Map<String, CattleIsar> _cattleMap = {};
  final Map<String, BreedCategoryIsar> _breedMap = {};
  final Map<String, AnimalTypeIsar> _animalTypeMap = {};
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedAnimalType = 'All';
  final List<AnimalTypeIsar> _animalTypes = [];
  final TextEditingController _searchController = TextEditingController();
  String _selectedDateRange = 'All Time';
  String _selectedCattleId = 'All';
  final List<String> _tagIds = [];

  final List<String> _dateRangeOptions = [
    'Today',
    '7 Days',
    '30 Days',
    '90 Days',
    'All Time',
  ];

  @override
  void initState() {
    super.initState();
    // Initialize repository dependencies from GetIt
    _healthRepository = GetIt.instance<HealthRepository>();
    // Unused repository initialization removed

    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Load all cattle
      final allCattle = await GetIt.instance<Isar>().cattleIsars.where().findAll();
      final Map<String, CattleIsar> cattleMap = {};

      // Get all health records
      final allRecords = await GetIt.instance<Isar>().healthRecordIsars.where().findAll();

      // Only add cattle that have health records
      for (final record in allRecords) {
        final cattleId = record.cattleId;
        if (cattleId != null) {
          final cattle = allCattle
              .where((c) => c.tagId?.toLowerCase() == cattleId.toLowerCase())
              .firstOrNull;
          if (cattle != null) {
            cattleMap[cattleId] = cattle;
          }
        }
      }

      if (mounted) {
        setState(() {
          _cattleMap = cattleMap;
          _healthRecords = allRecords;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error loading health records: $e')),
          );
        }
      }
    }
  }

  List<HealthRecordIsar> _filterHealthRecords() {
    List<HealthRecordIsar> filteredRecords = List.from(_healthRecords);

    if (_searchQuery.isNotEmpty) {
      final lowercaseQuery = _searchQuery.toLowerCase();
      filteredRecords = filteredRecords.where((record) {
        final cattle = _cattleMap[record.cattleId ?? ''];
        final conditionLower = record.condition?.toLowerCase() ?? '';
        final diagnosisLower = record.diagnosis?.toLowerCase() ?? '';
        final cattleName = cattle?.name?.toLowerCase() ?? '';
        final cattleTagId = cattle?.tagId?.toLowerCase() ?? '';

        return conditionLower.contains(lowercaseQuery) ||
            diagnosisLower.contains(lowercaseQuery) ||
            cattleName.contains(lowercaseQuery) ||
            cattleTagId.contains(lowercaseQuery);
      }).toList();
    }

    // Sort records by date (newest first)
    filteredRecords.sort((a, b) {
      if (a.date == null && b.date == null) return 0;
      if (a.date == null) return 1;
      if (b.date == null) return -1;
      return b.date!.compareTo(a.date!);
    });

    if (_selectedDateRange != 'All Time') {
      filteredRecords = filteredRecords.where((record) {
        if (record.date == null) return false;

        final recordDate = record.date!;
        switch (_selectedDateRange) {
          case 'Today':
            final today = DateTime.now();
            return recordDate.year == today.year &&
                recordDate.month == today.month &&
                recordDate.day == today.day;
          case '7 Days':
            final sevenDaysAgo =
                DateTime.now().subtract(const Duration(days: 7));
            return recordDate.isAfter(sevenDaysAgo);
          case '30 Days':
            final thirtyDaysAgo =
                DateTime.now().subtract(const Duration(days: 30));
            return recordDate.isAfter(thirtyDaysAgo);
          case '90 Days':
            final ninetyDaysAgo =
                DateTime.now().subtract(const Duration(days: 90));
            return recordDate.isAfter(ninetyDaysAgo);
          default:
            return true;
        }
      }).toList();
    }

    if (_selectedCattleId != 'All') {
      filteredRecords = filteredRecords.where((record) {
        return record.cattleId == _selectedCattleId;
      }).toList();
    }

    if (_selectedAnimalType != 'All') {
      filteredRecords = filteredRecords.where((record) {
        final cattle = _cattleMap[record.cattleId ?? ''];
        if (cattle == null) return false;

        final animalType = _animalTypeMap[cattle.animalTypeId ?? ''];
        return animalType?.name == _selectedAnimalType;
      }).toList();
    }

    return filteredRecords;
  }

  Future<void> _deleteHealthRecord(int id) async {
    try {
      await _healthRepository.deleteHealthRecord(id);
      _loadData();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Health record deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error deleting health record: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getFormattedDate(DateTime? date) {
    if (date == null) return 'N/A';
    return DateFormat.yMMMd().format(date);
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _searchController.clear();
      _selectedAnimalType = 'All';
      _selectedCattleId = 'All';
      _selectedDateRange = 'All Time';
    });
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search Row
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by cattle name or tag ID',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          // Filter Row
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: Row(
              children: [
                // Animal Type Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Types'),
                        ),
                        ..._animalTypes.map((type) => PopupMenuItem(
                              value: type.name ?? 'Unknown',
                              child: Text(type.name ?? 'Unknown'),
                            )),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedAnimalType = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedAnimalType,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Cattle Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Cattle'),
                        ),
                        ..._tagIds.map((tagId) {
                          final cattle = _cattleMap[tagId];
                          return PopupMenuItem(
                            value: tagId,
                            child: Text(cattle?.name ?? tagId),
                          );
                        }),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedCattleId = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedCattleId == 'All'
                                    ? 'All Cattle'
                                    : _cattleMap[_selectedCattleId]?.name ??
                                        _selectedCattleId,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Date Range Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => _dateRangeOptions
                          .map((range) => PopupMenuItem(
                                value: range,
                                child: Text(range),
                              ))
                          .toList(),
                      onSelected: (value) {
                        setState(() {
                          _selectedDateRange = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedDateRange,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Active Filters
          if (_selectedAnimalType != 'All' ||
              _selectedCattleId != 'All' ||
              _selectedDateRange != 'All Time' ||
              _searchQuery.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
              child: Row(
                children: [
                  const Text(
                    'Active Filters:',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (_selectedDateRange != 'All Time')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_selectedDateRange),
                        onDeleted: () =>
                            setState(() => _selectedDateRange = 'All Time'),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  if (_selectedAnimalType != 'All')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_selectedAnimalType),
                        onDeleted: () =>
                            setState(() => _selectedAnimalType = 'All'),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  if (_selectedCattleId != 'All')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_cattleMap[_selectedCattleId]?.name ??
                            _selectedCattleId),
                        onDeleted: () =>
                            setState(() => _selectedCattleId = 'All'),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  TextButton(
                    onPressed: _clearFilters,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      minimumSize: const Size(0, 24),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: const Text(
                      'Clear All',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          const Divider(height: 1),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Health Records'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddHealthRecordDialog(context);
        },
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildSearchAndFilterSection(),
                Expanded(
                  child: _filterHealthRecords().isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.medical_services,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                'No health records found',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Add a health record to start tracking',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[500],
                                ),
                              ),
                              const SizedBox(height: 24),
                              ElevatedButton.icon(
                                onPressed: () {
                                  _showAddHealthRecordDialog(context);
                                },
                                icon: const Icon(Icons.add),
                                label: const Text('Add Health Record'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF2E7D32),
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        )
                      : RefreshIndicator(
                          onRefresh: _loadData,
                          child: ListView.builder(
                            padding: const EdgeInsets.only(top: 8, bottom: 88),
                            itemCount: _filterHealthRecords().length,
                            itemBuilder: (context, index) {
                              final record = _filterHealthRecords()[index];
                              return _buildHealthRecordCard(record);
                            },
                          ),
                        ),
                ),
              ],
            ),
    );
  }

  void _showAddHealthRecordDialog(BuildContext context) {
    if (!mounted) return;
    final navigatorContext = context;

    showDialog(
      context: navigatorContext,
      builder: (context) => HealthRecordFormDialog(
        cattle: _cattleMap.values.toList(),
        onSave: (record) async {
          Navigator.pop(context);
          await _healthRepository.saveHealthRecord(record);
          if (!mounted) return;
          await _loadData();
        },
      ),
    );
  }

  Widget _buildHealthRecordCard(HealthRecordIsar record) {
    final cattle = _cattleMap[record.cattleId];
    if (cattle == null) return const SizedBox.shrink();

    final breed = _breedMap[cattle.breedId];
    final recordDate = _getFormattedDate(record.date);

    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          _showHealthRecordDetails(record);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    backgroundColor: Colors.teal.shade100,
                    child:
                        const Icon(Icons.medical_services, color: Colors.teal),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cattle.name ?? 'Unknown',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          'Tag ID: ${cattle.tagId ?? 'Unknown'}',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        recordDate,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        breed?.name ?? 'Unknown Breed',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const Divider(height: 24),
              _buildInfoRow('Record Type', record.recordType),
              _buildInfoRow('Diagnosis', record.diagnosis),
              _buildInfoRow('Treatment', record.treatment),
              if (record.medicine != null && record.medicine!.isNotEmpty)
                _buildInfoRow('Medicine',
                    '${record.medicine} (${record.dose} ${record.dosageUnit})'),
              if (record.followUpRequired != null && record.followUpRequired!)
                _buildInfoRow(
                  'Follow-up',
                  'Required on ${record.followUpDate != null ? _getFormattedDate(record.followUpDate) : 'N/A'}',
                ),
              if (record.isResolved != null && record.isResolved!)
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'Resolved',
                    style: TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showHealthRecordDetails(HealthRecordIsar record) {
    final cattle = _cattleMap[record.cattleId];
    if (cattle == null) return;

    final breed = _breedMap[cattle.breedId];
    final animalType = _animalTypeMap[cattle.animalTypeId];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Health Record Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Cattle', cattle.name ?? 'Unknown'),
              _buildDetailRow('Tag ID', cattle.tagId ?? 'Unknown'),
              _buildDetailRow('Breed', breed?.name ?? 'Unknown'),
              _buildDetailRow('Type', animalType?.name ?? 'Unknown'),
              _buildDetailRow('Record Type', record.recordType),
              _buildDetailRow('Date', _getFormattedDate(record.date)),
              _buildDetailRow('Diagnosis', record.diagnosis),
              _buildDetailRow('Details', record.details),
              _buildDetailRow('Treatment', record.treatment),
              if (record.medicine != null && record.medicine!.isNotEmpty)
                _buildDetailRow('Medicine',
                    '${record.medicine} (${record.dose} ${record.dosageUnit})'),
              _buildDetailRow(
                  'Cost',
                  record.cost != null
                      ? '\$${record.cost!.toStringAsFixed(2)}'
                      : 'N/A'),
              _buildDetailRow(
                  'Follow-up Required',
                  record.followUpRequired != null && record.followUpRequired!
                      ? 'Yes'
                      : 'No'),
              if (record.followUpRequired != null &&
                  record.followUpRequired! &&
                  record.followUpDate != null)
                _buildDetailRow(
                    'Follow-up Date', _getFormattedDate(record.followUpDate)),
              _buildDetailRow(
                  'Is Follow-up',
                  record.isFollowUp != null && record.isFollowUp!
                      ? 'Yes'
                      : 'No'),
              if (record.previousRecordId != null &&
                  record.previousRecordId!.isNotEmpty)
                _buildDetailRow('Previous Record ID', record.previousRecordId!),
              _buildDetailRow(
                  'Resolved',
                  record.isResolved != null && record.isResolved!
                      ? 'Yes'
                      : 'No'),
              _buildDetailRow('Notes', record.notes),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _editHealthRecord(record);
            },
            child: const Text('Edit'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteHealthRecord(record.id);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _editHealthRecord(HealthRecordIsar record) {
    final cattle = _cattleMap[record.cattleId ?? ''];
    if (cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => HealthRecordFormDialog(
        cattle: _cattleMap.values.toList(),
        healthRecord: record,
        onSave: (updatedRecord) async {
          Navigator.pop(context);
          await _healthRepository.saveHealthRecord(updatedRecord);
          await _loadData();
        },
      ),
    );
  }

  Widget _buildInfoRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$label: ',
            style: const TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
          Expanded(
            child: Text(
              value ?? 'N/A',
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String? value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(value ?? 'N/A'),
          ),
        ],
      ),
    );
  }
}
