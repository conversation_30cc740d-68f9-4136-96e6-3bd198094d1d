import 'package:flutter/material.dart';
import '../models/weight_record_isar.dart';
import '../controllers/weight_controller.dart';
// Unused import removed
import '../widgets/weight_record_card.dart';
import '../dialogs/weight_form_dialog.dart';

/// Weight records tab - pure UI component that reads from controller
class WeightRecordsTab extends StatelessWidget {
  final WeightController controller;

  const WeightRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (controller.filteredRecords.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.scale, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'No weight records found',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              'Add your first weight record to get started',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: controller.filteredRecords.length,
      itemBuilder: (context, index) {
        final record = controller.filteredRecords[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: WeightRecordCard(
            record: record,
            onTap: () => _showRecordDetails(context, record),
            onEdit: () => _editRecord(context, record),
            onDelete: () => _deleteRecord(context, record),
          ),
        );
      },
    );
  }

  void _showRecordDetails(BuildContext context, WeightRecordIsar record) {
    // TODO: Navigate to weight record details
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Details for ${record.formattedWeight}')),
    );
  }

  void _editRecord(BuildContext context, WeightRecordIsar record) {
    showDialog(
      context: context,
      builder: (context) => WeightFormDialog(
        cattle: controller.allCattle,
        existingRecord: record,
      ),
    );
  }

  void _deleteRecord(BuildContext context, WeightRecordIsar record) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Weight Record'),
        content: Text('Are you sure you want to delete this weight record (${record.formattedWeight})?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              // TODO: Implement delete functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Delete functionality coming soon')),
              );
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
