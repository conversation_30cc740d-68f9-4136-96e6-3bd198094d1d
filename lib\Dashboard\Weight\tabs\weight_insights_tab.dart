import 'package:flutter/material.dart';
import '../controllers/weight_controller.dart';

/// Weight insights tab - pure UI component that reads from controller
class WeightInsightsTab extends StatelessWidget {
  final WeightController controller;

  const WeightInsightsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recommendations Section
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Recommendations',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  if (controller.insights.recommendations.isEmpty)
                    const Text(
                      'No recommendations at this time. Your herd is performing well!',
                      style: TextStyle(color: Colors.grey),
                    )
                  else
                    ...controller.insights.recommendations.map((rec) => ListTile(
                      leading: const Icon(Icons.lightbulb, color: Colors.orange),
                      title: Text(rec.title),
                      subtitle: Text(rec.description),
                      trailing: Chip(
                        label: Text(rec.priority),
                        backgroundColor: Colors.orange.withValues(alpha: 0.1),
                      ),
                    )),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Health Insights
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Health Insights',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    leading: const Icon(Icons.health_and_safety, color: Colors.green),
                    title: const Text('Body Condition'),
                    subtitle: Text('Average: ${controller.insights.health.averageBodyCondition.toStringAsFixed(1)}'),
                    trailing: Text(controller.insights.health.consistencyRating),
                  ),
                  if (controller.insights.health.healthAlerts > 0)
                    ListTile(
                      leading: const Icon(Icons.warning, color: Colors.orange),
                      title: const Text('Health Alerts'),
                      subtitle: Text('${controller.insights.health.healthAlerts} alerts detected'),
                    ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Trend Analysis
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Trend Analysis',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  ListTile(
                    leading: const Icon(Icons.trending_up, color: Colors.blue),
                    title: const Text('Monthly Trend'),
                    subtitle: Text('${controller.insights.trends.monthlyChange.toStringAsFixed(1)} kg change'),
                    trailing: Text(controller.insights.trends.trendDirection),
                  ),
                  ListTile(
                    leading: const Icon(Icons.timeline, color: Colors.purple),
                    title: const Text('Seasonal Pattern'),
                    subtitle: Text(controller.insights.trends.seasonalPattern),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
