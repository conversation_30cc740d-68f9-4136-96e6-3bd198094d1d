import 'package:flutter/material.dart';
import '../widgets/dashboard_menu_item.dart';
import 'package:get_it/get_it.dart';
import 'Farm Setup/services/farm_setup_repository.dart';
import 'Farm Setup/models/farm_isar.dart';
// import 'Farm Setup/screens/farm_setup_screen.dart';
// import 'Cattle/screens/cattle_screen.dart'; // No longer needed - using named routes
// import 'Reports/screens/reports_screen.dart';
// import 'Transactions/screens/transactions_screen.dart';
// import 'Milk Records/screens/milk_screen.dart';
// import 'Events/screens/events_screen.dart';
// import 'Breeding/screens/breeding_screen.dart';
// import 'Health/screens/health_screen.dart';
// import 'Weight/screens/weight_screen.dart';
import 'widgets/farm_selection_drawer.dart';
import '../constants/app_bar.dart';
import '../constants/app_colors.dart';
import '../routes/app_routes.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  FarmIsar? _selectedFarm;
  bool _isLoading = true;
  final ValueNotifier<FarmIsar?> _farmChangeNotifier = ValueNotifier<FarmIsar?>(null);

  @override
  void initState() {
    super.initState();
    _loadSelectedFarm();
    _setupFarmChangeListener();
  }

  @override
  void dispose() {
    _farmChangeNotifier.dispose();
    super.dispose();
  }

  void _setupFarmChangeListener() {
    _farmChangeNotifier.addListener(_onFarmChanged);
  }

  void _onFarmChanged() {
    _loadSelectedFarm();
  }

  Future<void> _loadSelectedFarm() async {
    try {
      setState(() => _isLoading = true);
      final farm = await _farmSetupRepository.getActiveFarm();
      setState(() {
        _selectedFarm = farm;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading selected farm: $e');
      setState(() => _isLoading = false);
    }
  }



  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final selectedFarmName = _selectedFarm?.name ?? 'My Cattle Manager';
    final screenSize = MediaQuery.of(context).size;
    int crossAxisCount = screenSize.width > 600 ? 3 : 2;

    return Scaffold(
      appBar: AppBarConfig.withDrawer(
        title: selectedFarmName,
        context: context,
        actions: [
          AppBarConfig.notificationsButton(
            onPressed: () {
              // Handle notifications button tap
            },
          ),
        ],
      ),
      drawer: const FarmSelectionDrawer(),
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(15),
                  ),
                      child: GridView.count(
                        padding: const EdgeInsets.all(12),
                        shrinkWrap: false,
                        physics: const BouncingScrollPhysics(),
                        crossAxisCount: crossAxisCount,
                        mainAxisSpacing: 12.0,
                        crossAxisSpacing: 12.0,
                        childAspectRatio: 1.0,
                        children: [
                          DashboardMenuItem(
                            title: 'Cattle Records',
                            icon: Icons.pets,
                            color: Colors.blue,
                            onTap: () => Navigator.pushNamed(context, AppRoutes.cattle),
                          ),
                          DashboardMenuItem(
                            title: 'Milk Records',
                            icon: Icons.local_drink,
                            color: Colors.green,
                            onTap: () => Navigator.pushNamed(context, AppRoutes.milk),
                          ),
                          DashboardMenuItem(
                            title: 'Breeding',
                            icon: Icons.favorite,
                            color: Colors.pink,
                            onTap: () => Navigator.pushNamed(context, AppRoutes.breeding),
                          ),
                          DashboardMenuItem(
                            title: 'Health',
                            icon: Icons.medical_services,
                            color: Colors.teal,
                            onTap: () => Navigator.pushNamed(context, AppRoutes.health),
                          ),
                          DashboardMenuItem(
                            title: 'Weight',
                            icon: Icons.monitor_weight,
                            color: Colors.indigo,
                            onTap: () => Navigator.pushNamed(context, AppRoutes.weight),
                          ),
                          DashboardMenuItem(
                            title: 'Events',
                            icon: Icons.event,
                            color: Colors.orange,
                            onTap: () => Navigator.pushNamed(context, AppRoutes.events),
                          ),
                          DashboardMenuItem(
                            title: 'Transactions',
                            icon: Icons.account_balance_wallet,
                            color: Colors.purple,
                            onTap: () => Navigator.pushNamed(context, AppRoutes.transactions),
                          ),
                          DashboardMenuItem(
                            title: 'Reports',
                            icon: Icons.bar_chart,
                            color: Colors.red,
                            onTap: () => Navigator.pushNamed(context, AppRoutes.reports),
                          ),
                          DashboardMenuItem(
                            title: 'Farm Setup',
                            icon: Icons.settings,
                            color: Colors.deepPurple,
                            onTap: () => Navigator.pushNamed(context, AppRoutes.settings),
                          ),
                          DashboardMenuItem(
                            title: 'User Account',
                            icon: Icons.account_circle,
                            color: Colors.brown,
                            onTap: () {
                              // TODO: Navigate to User Account screen when available
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('User Account - Coming Soon')),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    height: 48.0,
                    child: ElevatedButton.icon(
                      icon: const Icon(Icons.sync),
                      label: const Text('Sync Data'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      onPressed: () {
                        // Handle sync button tap
                      },
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
