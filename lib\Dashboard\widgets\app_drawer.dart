import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../Farm Setup/services/farm_setup_repository.dart';
import '../Help/screens/help_screen.dart';
import '../Farm Setup/models/farm_isar.dart';
import '../Farm Setup/screens/farm_info_screen.dart';
// import '../Reports/screens/reports_screen.dart';
import '../Notifications/screens/notifications_screen.dart';
// import '../User Account/screens/user_account_screen.dart'; // TODO: Create this file
// import '../User Account/screens/cloud_login_screen.dart'; // TODO: Create this file
// import '../User Account/services/cloud_authentication_service.dart'; // TODO: Create this file
// import '../../constants/app_constants.dart'; // TODO: Use when AppConstants is available
// import '../../utils/message_utils.dart'; // TODO: Use when needed
// import '../../services/permission_service.dart'; // TODO: Create this file

// Constants for strings and padding values
class _AppStrings {
  static const String manageYourFarm = 'Manage Your Farm';
  static const String dashboard = 'Dashboard';
  static const String farmSetup = 'Farm Setup';

  static const String notifications = 'Notifications';
  static const String helpSupport = 'Help & Support';
  static const String version = 'Version 1.0.0';
  static const String errorLoadingFarm = 'Error loading farm: ';
}

class _AppPadding {
  static const double medium = 16.0;
  static const double large = 24.0;
}

class _ResponsiveHelper {
  static bool isSmallScreen(BuildContext context) {
    return MediaQuery.of(context).size.width < 600;
  }

  static bool isMediumScreen(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= 600 && width < 900;
  }

  static double getAvatarRadius(BuildContext context) {
    if (isSmallScreen(context)) return 35.0;
    if (isMediumScreen(context)) return 45.0;
    return 50.0;
  }

  static double getHeaderPadding(BuildContext context) {
    if (isSmallScreen(context)) return _AppPadding.medium;
    if (isMediumScreen(context)) return _AppPadding.large;
    return _AppPadding.large + 8.0;
  }

  static double getSpacing(BuildContext context) {
    if (isSmallScreen(context)) return 8.0;
    if (isMediumScreen(context)) return 12.0;
    return 16.0;
  }
}

class AppDrawer extends StatefulWidget {
  const AppDrawer({Key? key}) : super(key: key);

  @override
  State<AppDrawer> createState() => _AppDrawerState();
}

class _AppDrawerState extends State<AppDrawer> {
  final FarmSetupRepository _farmSetupRepository = GetIt.instance<FarmSetupRepository>();
  // CloudAuthenticationService? _authService; // TODO: Implement when service is created
  FarmIsar? _currentFarm;
  // bool _isLoading = true; // TODO: Use when needed
  bool _isGuestMode = false;

  @override
  void initState() {
    super.initState();
    _loadFarm();
    _setupFarmChangeListener();
    _initializeAuthService();
  }

  @override
  void dispose() {
    // _farmSetupHandler.removeListener(_onFarmChanged);
    // _authService?.removeListener(_onAuthStateChanged); // TODO: Implement when service is created
    super.dispose();
  }

  void _setupFarmChangeListener() {
    // _farmSetupHandler.addListener(_onFarmChanged);
  }



  void _initializeAuthService() {
    // TODO: Implement when CloudAuthenticationService is created
    // try {
    //   _authService = GetIt.instance<CloudAuthenticationService>();
    //   _authService!.addListener(_onAuthStateChanged);
    //   _checkAuthenticationState();
    // } catch (e) {
    //   // CloudAuthenticationService not available, user is in guest mode
      setState(() {
        _isGuestMode = true;
      });
    // }
  }

  // TODO: Implement when CloudAuthenticationService is created
  // void _onAuthStateChanged() {
  //   if (mounted) {
  //     _checkAuthenticationState();
  //   }
  // }



  Future<void> _loadFarm() async {
    try {
      // TODO: Check if database is currently switching when service is created
      // if (_authService?.isDatabaseSwitching == true) {
      //   // Wait for database switching to complete
      //   if (mounted) {
      //     setState(() => _isLoading = false);
      //   }
      //   return;
      // }

      // Get the user's single farm
      final farms = await _farmSetupRepository.getAllFarms();

      if (!mounted) return;

      FarmIsar? currentFarm;
      if (farms.isNotEmpty) {
        // Single farm per user - take the first (and only) farm
        currentFarm = farms.first;
      }

      setState(() {
        _currentFarm = currentFarm;
        // _isLoading = false; // TODO: Use when needed
      });
    } catch (e, stackTrace) {
      debugPrint('${_AppStrings.errorLoadingFarm}$e\n$stackTrace');
      if (mounted) {
        // setState(() => _isLoading = false); // TODO: Use when needed
      }
    }
  }

  // Helper for navigation to reduce repetition
  void _navigateTo(Widget screen) {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => screen),
    );
  }

  Future<void> _handleLogout() async {
    // TODO: Implement when CloudAuthenticationService is created
    // try {
    //   if (_authService != null) {
    //     await _authService!.signOut();
    //     if (mounted) {
    //       Navigator.pop(context); // Close drawer
    //       MessageUtils.showSuccess(context, 'Logged out successfully');
    //     }
    //   }
    // } catch (e) {
    //   if (mounted) {
    //     Navigator.pop(context); // Close drawer
    //     MessageUtils.showError(context, 'Logout failed: $e');
    //   }
    // }
    if (mounted) {
      Navigator.pop(context); // Close drawer
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Logout functionality coming soon')),
      );
    }
  }

  Widget _buildUserAuthSection() {
    if (_isGuestMode) {
      return Container(
        margin: EdgeInsets.symmetric(
          horizontal: _ResponsiveHelper.getHeaderPadding(context),
          vertical: _ResponsiveHelper.getSpacing(context),
        ),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.orange.shade200),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.orange.shade700,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Guest Mode',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange.shade800,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Sign in to sync your data and access all features.',
              style: TextStyle(
                color: Colors.orange.shade700,
                fontSize: 12,
              ),
            ),
          ],
        ),
      );
    } else {
      // TODO: Authenticated user section - implement when service is created
      // final userEmail = _authService?.userEmail ?? '';
      // final displayName = _authService?.currentUserDisplayName ?? 'User';
      // final isVerified = _authService?.isEmailVerified ?? false;
      const userEmail = '';
      const displayName = 'User';

      return Container(
        margin: EdgeInsets.symmetric(
          horizontal: _ResponsiveHelper.getHeaderPadding(context),
          vertical: _ResponsiveHelper.getSpacing(context),
        ),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.green.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.green.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: const Color(0xFF4CAF50), // TODO: Use AppConstants.primaryColor when available
                  child: Text(
                    displayName.isNotEmpty ? displayName[0].toUpperCase() : 'U',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        displayName,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 14,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (userEmail.isNotEmpty)
                        Text(
                          userEmail,
                          style: const TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      // Display role prominently without duplicate name
                      _buildUserRoleDisplay(),
                    ],
                  ),
                ),

              ],
            ),
          ],
        ),
      );
    }
  }

  /// Build user role display without duplicate name
  Widget _buildUserRoleDisplay() {
    // TODO: Implement when PermissionService is created
    // try {
    //   final permissionService = GetIt.instance<PermissionService>();
    //   final roleName = permissionService.currentUserRoleName;

    //   if (roleName.isNotEmpty) {
    //     return Container(
    //       margin: const EdgeInsets.only(top: 2),
    //       padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
    //       decoration: BoxDecoration(
    //         color: const Color(0xFF4CAF50).withAlpha(26), // 0.1 * 255 = 26
    //         borderRadius: BorderRadius.circular(8),
    //         border: Border.all(
    //           color: const Color(0xFF4CAF50).withAlpha(77), // 0.3 * 255 = 77
    //           width: 1,
    //         ),
    //       ),
    //       child: Text(
    //         roleName.toUpperCase(),
    //         style: const TextStyle(
    //           fontSize: 10,
    //           fontWeight: FontWeight.bold,
    //           color: Color(0xFF4CAF50),
    //           letterSpacing: 0.5,
    //         ),
    //       ),
    //     );
    //   }
    // } catch (e) {
    //   // Permission service not available, fallback to basic display
    // }

    return const SizedBox.shrink();
  }

  Widget _buildBottomAuthButtons() {
    if (_isGuestMode) {
      return Padding(
        padding: EdgeInsets.all(_ResponsiveHelper.getHeaderPadding(context)),
        child: Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  // TODO: Navigate to CloudLoginScreen when created
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Cloud login coming soon')),
                  );
                },
                icon: const Icon(Icons.login),
                label: const Text(
                  'Sign In for Full Access',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4CAF50), // TODO: Use AppConstants.primaryColor when available
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      );
    } else {
      // Authenticated user buttons
      return Padding(
        padding: EdgeInsets.all(_ResponsiveHelper.getHeaderPadding(context)),
        child: Column(
          children: [
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () {
                  // TODO: Navigate to UserAccountScreen when created
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('User account screen coming soon')),
                  );
                },
                icon: const Icon(Icons.person),
                label: const Text('Profile'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  side: const BorderSide(color: Color(0xFF4CAF50)), // TODO: Use AppConstants.primaryColor when available
                  foregroundColor: const Color(0xFF4CAF50), // TODO: Use AppConstants.primaryColor when available
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _handleLogout,
                icon: const Icon(Icons.logout),
                label: const Text('Logout'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  side: const BorderSide(color: Colors.red),
                  foregroundColor: Colors.red,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      );
    }
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
    bool showDivider = false,
  }) {
    final theme = Theme.of(context);
    final isSmall = _ResponsiveHelper.isSmallScreen(context);

    return Column(
      children: [
        ListTile(
          contentPadding: EdgeInsets.symmetric(
            horizontal: _ResponsiveHelper.getHeaderPadding(context),
            vertical: isSmall ? 4.0 : 8.0,
          ),
          leading: Icon(
            icon,
            color: iconColor ?? theme.colorScheme.primary,
            size: isSmall ? 20.0 : 24.0, // Responsive icon size
          ),
          title: Text(
            title,
            style: isSmall
                ? theme.textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                  )
                : theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
            maxLines: 2, // Allow text to wrap
            overflow: TextOverflow.ellipsis,
          ),
          onTap: onTap,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        if (showDivider)
          Divider(
            height: 1,
            color: theme.dividerColor,
            indent: _ResponsiveHelper.getHeaderPadding(context),
            endIndent: _ResponsiveHelper.getHeaderPadding(context),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final mediaQuery = MediaQuery.of(context);

    return Drawer(
      width: _ResponsiveHelper.isSmallScreen(context)
          ? 280.0
          : _ResponsiveHelper.isMediumScreen(context)
              ? 320.0
              : 360.0, // Responsive drawer width
      child: Container(
        color: colorScheme.surface,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(
                top: mediaQuery.padding.top + _ResponsiveHelper.getHeaderPadding(context),
                bottom: _ResponsiveHelper.getHeaderPadding(context),
                left: _ResponsiveHelper.getHeaderPadding(context),
                right: _ResponsiveHelper.getHeaderPadding(context),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center, // Center align everything
                children: [
                  CircleAvatar(
                    radius: _ResponsiveHelper.getAvatarRadius(context),
                    backgroundColor: colorScheme.secondaryContainer,
                    child: Icon(
                      Icons.person,
                      size: _ResponsiveHelper.getAvatarRadius(context) * 0.8, // Responsive icon size
                      color: colorScheme.onSecondaryContainer,
                    ),
                  ),
                  SizedBox(height: _ResponsiveHelper.getSpacing(context)),
                  Text(
                    _currentFarm?.name ?? 'My Farm',
                    style: _ResponsiveHelper.isSmallScreen(context)
                        ? textTheme.titleLarge?.copyWith(
                            color: colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                          )
                        : textTheme.headlineSmall?.copyWith(
                            color: colorScheme.onSurface,
                            fontWeight: FontWeight.bold,
                          ),
                    textAlign: TextAlign.center, // Center align text
                    maxLines: 2, // Allow text to wrap to 2 lines
                    overflow: TextOverflow.ellipsis,
                  ),
                  SizedBox(height: _ResponsiveHelper.getSpacing(context) * 0.5),
                  Text(
                    _AppStrings.manageYourFarm,
                    style: _ResponsiveHelper.isSmallScreen(context)
                        ? textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurface.withAlpha(179),
                          )
                        : textTheme.bodyMedium?.copyWith(
                            color: colorScheme.onSurface.withAlpha(179),
                          ),
                    textAlign: TextAlign.center, // Center align text
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  // User Authentication Section
                  _buildUserAuthSection(),

                  Divider(height: 1, color: theme.dividerColor),
                  _buildDrawerItem(
                    icon: Icons.dashboard,
                    title: _AppStrings.dashboard,
                    onTap: () => Navigator.pop(context),
                    iconColor: Colors.blue,
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.settings_applications,
                    title: _AppStrings.farmSetup,
                    onTap: () => _navigateTo(const FarmInfoScreen()),
                    iconColor: Colors.green,
                    showDivider: true,
                  ),
                  // _buildDrawerItem(
                  //   icon: Icons.analytics,
                  //   title: _AppStrings.reportsAnalytics,
                  //   onTap: () => _navigateTo(const ReportsScreen()),
                  //   iconColor: Colors.purple,
                  //   showDivider: true,
                  // ),
                  _buildDrawerItem(
                    icon: Icons.notifications,
                    title: _AppStrings.notifications,
                    onTap: () => _navigateTo(const NotificationsScreen()),
                    iconColor: Colors.teal,
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.help_outline,
                    title: _AppStrings.helpSupport,
                    onTap: () => _navigateTo(const HelpScreen()),
                    iconColor: Colors.cyan,
                    showDivider: true,
                  ),
                ],
              ),
            ),
            // Bottom authentication buttons
            _buildBottomAuthButtons(),
            // Version info
            Padding(
              padding: EdgeInsets.only(
                left: _ResponsiveHelper.getHeaderPadding(context),
                right: _ResponsiveHelper.getHeaderPadding(context),
                top: 8,
                bottom: _ResponsiveHelper.getHeaderPadding(context), // Added bottom padding
              ),
              child: Center(
                child: Text(
                  _AppStrings.version,
                  style: _ResponsiveHelper.isSmallScreen(context)
                      ? textTheme.labelSmall?.copyWith(
                          color: colorScheme.onSurface,
                        )
                      : textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
