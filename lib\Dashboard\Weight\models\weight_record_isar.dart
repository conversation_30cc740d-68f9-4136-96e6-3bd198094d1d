import 'package:isar/isar.dart';
import '../../Cattle/models/cattle_isar.dart';

part 'weight_record_isar.g.dart';

/// Measurement method for weight records
enum MeasurementMethod {
  scale,
  tape,
  visualEstimate,
}

/// Measurement quality rating
enum MeasurementQuality {
  excellent,
  good,
  fair,
  poor,
}

/// Weight unit for measurements
enum WeightUnit {
  kg,
  lbs,
}

/// Weight goal type
enum WeightGoal {
  gain,
  maintain,
  lose,
}

/// Health status for weight context
enum HealthStatus {
  healthy,
  sick,
  recovering,
}

/// Feeding status for weight context
enum FeedingStatus {
  normal,
  increased,
  decreased,
  fasting,
}

/// Feed quality rating
enum FeedQuality {
  excellent,
  good,
  fair,
  poor,
}

/// Season for weight measurement context
enum Season {
  spring,
  summer,
  autumn,
  winter,
}

/// Weather conditions during measurement
enum WeatherConditions {
  sunny,
  cloudy,
  rainy,
  stormy,
  hot,
  cold,
}

/// Goal type for weight goals
enum GoalType {
  weightGain,
  weightLoss,
  weightMaintenance,
}

/// Goal status
enum GoalStatus {
  active,
  achieved,
  paused,
  cancelled,
}

@collection
class WeightRecordIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index()
  String? farmBusinessId;

  // IsarLink relationship to cattle - replaces String cattleBusinessId
  final cattle = IsarLink<CattleIsar>();

  // Core weight measurement data
  double weight = 0.0;
  DateTime? measurementDate;

  @enumerated
  MeasurementMethod measurementMethod = MeasurementMethod.scale;

  @enumerated
  MeasurementQuality measurementQuality = MeasurementQuality.good;

  String? measurementLocation;
  bool? isEstimate;
  double? confidenceLevel;

  // Body condition scoring
  double? bodyConditionScore;
  String? bodyConditionNotes;

  // Weight tracking and analysis
  double? previousWeight;
  double? weightGain;
  double? dailyGain;
  int? daysSinceLastMeasurement;

  // Weight goals and targets
  @enumerated
  WeightGoal weightGoal = WeightGoal.maintain;

  double? targetWeight;
  DateTime? targetDate;

  // Animal status information
  @enumerated
  HealthStatus healthStatus = HealthStatus.healthy;

  @enumerated
  FeedingStatus feedingStatus = FeedingStatus.normal;

  @enumerated
  FeedQuality feedQuality = FeedQuality.good;

  bool? isPregnant;
  int? pregnancyStage;

  @enumerated
  Season season = Season.spring;

  @enumerated
  WeatherConditions weatherConditions = WeatherConditions.sunny;

  // Validation and quality control
  bool? isValidated;
  DateTime? validatedAt;
  String? validatedBy;

  // Measurement context
  String? measuredBy;
  String? notes;

  @enumerated
  WeightUnit weightUnit = WeightUnit.kg;

  // Audit fields
  DateTime? createdAt;
  DateTime? updatedAt;
  String? createdBy;
  String? updatedBy;

  WeightRecordIsar();

  @override
  String toString() {
    return 'WeightRecordIsar{id: $id, businessId: $businessId, cattle: ${cattle.value?.businessId}, weight: $weight, measurementDate: $measurementDate}';
  }
}

@collection
class WeightGoalIsar {
  Id id = Isar.autoIncrement;

  @Index(unique: true)
  String? businessId;

  @Index()
  String? farmBusinessId;

  // IsarLink relationship to cattle - replaces String cattleBusinessId
  final cattle = IsarLink<CattleIsar>();

  // Goal details
  @enumerated
  GoalType goalType = GoalType.weightMaintenance;

  double? targetWeight;
  double? startingWeight;
  double? currentWeight;
  DateTime? startDate;
  DateTime? targetDate;
  DateTime? achievedDate;

  // Progress tracking
  double? weightToGain;
  double? weightToLose;
  double? dailyTargetGain;
  double? weeklyTargetGain;
  double? progressPercentage;
  bool? isAchieved;

  @enumerated
  GoalStatus status = GoalStatus.active;

  // Additional information
  String? notes;
  DateTime? createdAt;
  DateTime? updatedAt;

  WeightGoalIsar();
}

/// Extension methods for WeightRecordIsar presentation logic
extension WeightRecordIsarExtensions on WeightRecordIsar {
  /// Get formatted weight with unit
  String get formattedWeight {
    return '${weight.toStringAsFixed(1)} ${weightUnit.symbol}';
  }

  /// Get formatted measurement date
  String get formattedMeasurementDate {
    if (measurementDate == null) return 'Unknown';
    return '${measurementDate!.day}/${measurementDate!.month}/${measurementDate!.year}';
  }

  /// Get weight gain/loss indicator
  String get weightChangeIndicator {
    if (weightGain == null) return '';
    if (weightGain! > 0) return '+${weightGain!.toStringAsFixed(1)}';
    if (weightGain! < 0) return weightGain!.toStringAsFixed(1);
    return '0.0';
  }

  /// Get measurement quality color indicator
  String get qualityColorCode {
    switch (measurementQuality) {
      case MeasurementQuality.excellent:
        return '#4CAF50'; // Green
      case MeasurementQuality.good:
        return '#8BC34A'; // Light Green
      case MeasurementQuality.fair:
        return '#FF9800'; // Orange
      case MeasurementQuality.poor:
        return '#F44336'; // Red
    }
  }

  /// Get cattle name for display
  String get cattleName {
    return cattle.value?.name ?? 'Unknown Cattle';
  }

  /// Get cattle tag ID for display
  String get cattleTagId {
    return cattle.value?.tagId ?? 'Unknown';
  }
}

/// Extension methods for enum display values
extension MeasurementMethodExtension on MeasurementMethod {
  String get displayName {
    switch (this) {
      case MeasurementMethod.scale:
        return 'Scale';
      case MeasurementMethod.tape:
        return 'Tape Measure';
      case MeasurementMethod.visualEstimate:
        return 'Visual Estimate';
    }
  }
}

extension MeasurementQualityExtension on MeasurementQuality {
  String get displayName {
    switch (this) {
      case MeasurementQuality.excellent:
        return 'Excellent';
      case MeasurementQuality.good:
        return 'Good';
      case MeasurementQuality.fair:
        return 'Fair';
      case MeasurementQuality.poor:
        return 'Poor';
    }
  }
}

extension WeightUnitExtension on WeightUnit {
  String get displayName {
    switch (this) {
      case WeightUnit.kg:
        return 'Kilograms';
      case WeightUnit.lbs:
        return 'Pounds';
    }
  }

  String get symbol {
    switch (this) {
      case WeightUnit.kg:
        return 'kg';
      case WeightUnit.lbs:
        return 'lbs';
    }
  }
}

extension WeightGoalExtension on WeightGoal {
  String get displayName {
    switch (this) {
      case WeightGoal.gain:
        return 'Weight Gain';
      case WeightGoal.maintain:
        return 'Maintain Weight';
      case WeightGoal.lose:
        return 'Weight Loss';
    }
  }
}