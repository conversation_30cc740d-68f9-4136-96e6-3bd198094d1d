import 'package:flutter/material.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../controllers/cattle_weight_detail_controller.dart';
import '../../../constants/app_bar.dart';
import '../../../utils/message_utils.dart';
import '../../../constants/app_tabs.dart';
import 'weight_details_analytics_tab.dart';
import 'weight_details_records_tab.dart';

class WeightDetailsScreen extends StatefulWidget {
  final CattleIsar cattle;
  final VoidCallback onRefresh;

  const WeightDetailsScreen({
    Key? key,
    required this.cattle,
    required this.onRefresh,
  }) : super(key: key);

  @override
  State<WeightDetailsScreen> createState() => _WeightDetailsScreenState();
}

class _WeightDetailsScreenState extends State<WeightDetailsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late CattleWeightDetailController _controller;

  // Tab configuration using the reusable widget
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update colors
    });

    // Initialize controller
    _controller = CattleWeightDetailController(cattle: widget.cattle);
    _controller.addListener(_onControllerChanged);

    // Define tabs using the reusable widget configuration with multicolor
    _tabs = TabConfigurations.twoTabDetail(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab1Color: Colors.blue, // Blue for analytics
      tab2Label: 'Records',
      tab2Icon: Icons.list_alt,
      tab2Color: const Color(0xFF2E7D32), // Green for records
    );

    _initializeController();
  }

  @override
  void dispose() {
    _controller.removeListener(_onControllerChanged);
    _controller.dispose();
    _tabController.dispose();
    super.dispose();
  }

  void _initializeController() {
    try {
      _controller.initialize();
    } catch (e) {
      if (mounted) {
        MessageUtils.showError(context, 'Error loading weight records: $e');
      }
    }
  }

  void _onControllerChanged() {
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final cattleName = widget.cattle.name ?? 'Unknown Cattle';
    final tagId = widget.cattle.tagId ?? '';
    final displayName = (tagId.isNotEmpty) ? '$cattleName ($tagId)' : cattleName;

    return Scaffold(
      appBar: AppBarConfig.withBack(
        title: displayName,
        context: context,
        actions: _buildAppBarActions(),
      ),
      body: Column(
        children: [
          // Use the reusable tab bar widget with multicolor
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: const Color(0xFF2E7D32), // Green indicator
          ),
          // TabBarView
          Expanded(
            child: _controller.isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      WeightDetailsAnalyticsTab(
                        controller: _controller,
                      ),
                      WeightDetailsRecordsTab(
                        controller: _controller,
                      ),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildAppBarActions() {
    return [
      IconButton(
        icon: const Icon(Icons.refresh),
        onPressed: _refreshData,
        tooltip: 'Refresh',
      ),
    ];
  }

  void _refreshData() {
    _controller.refresh();
    widget.onRefresh(); // Refresh parent screen data
  }
}
